version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: fintrack-sqlserver
    environment:
      ACCEPT_EULA: Y
      SA_PASSWORD: SqlServer123!
      MSSQL_PID: Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - fintrack-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P FinTrack123! -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # FinTrack Application (for future use when containerized)
  # fintrack-app:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: fintrack-app
  #   ports:
  #     - "8080:8080"
  #   depends_on:
  #     sqlserver:
  #       condition: service_healthy
  #   environment:
  #     SPRING_PROFILES_ACTIVE: dev
  #     SPRING_DATASOURCE_URL: *******************************************************
  #     SPRING_DATASOURCE_USERNAME: sa
  #     SPRING_DATASOURCE_PASSWORD: FinTrack123!
  #   networks:
  #     - fintrack-network
  #   restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local

networks:
  fintrack-network:
    driver: bridge
