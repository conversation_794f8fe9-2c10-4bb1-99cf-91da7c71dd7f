package za.co.fintrack.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;

/**
 * Database configuration and connection verification
 */
@Slf4j
@Configuration
public class DatabaseConfig {

    @Value("${spring.datasource.url}")
    private String databaseUrl;

    @Value("${spring.datasource.username}")
    private String databaseUsername;

    /**
     * Database connection verification for development profile
     * This will run on application startup to verify database connectivity
     */
    @Bean
    @Profile("dev")
    public CommandLineRunner verifyDatabaseConnection(DataSource dataSource) {
        return args -> {
            try (Connection connection = dataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                
                log.info("=== Database Connection Verification ===");
                log.info("Database URL: {}", databaseUrl);
                log.info("Database Username: {}", databaseUsername);
                log.info("Database Product: {} {}", 
                    metaData.getDatabaseProductName(), 
                    metaData.getDatabaseProductVersion());
                log.info("Driver: {} {}", 
                    metaData.getDriverName(), 
                    metaData.getDriverVersion());
                log.info("Connection successful: {}", !connection.isClosed());
                log.info("Auto-commit: {}", connection.getAutoCommit());
                log.info("Transaction isolation: {}", connection.getTransactionIsolation());
                log.info("=== Database Connection Verified ===");
                
            } catch (Exception e) {
                log.error("Failed to connect to database: {}", e.getMessage(), e);
                throw new RuntimeException("Database connection failed", e);
            }
        };
    }
}
