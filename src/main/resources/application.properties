# ===================================================================
# FinTrack Application Configuration
# ===================================================================

# Application Information
spring.application.name=FinTrack
server.port=8080
server.servlet.context-path=/api/v1

# ===================================================================
# DATABASE CONFIGURATION - Microsoft SQL Server
# ===================================================================

# SQL Server Database Connection
spring.datasource.url=*************************************************************************************************************************
spring.datasource.username=sa
spring.datasource.password=SqlServer123!
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

# Connection Pool Configuration (HikariCP)
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.pool-name=FinTrackHikariCP

# ===================================================================
# JPA / HIBERNATE CONFIGURATION
# ===================================================================

# Hibernate Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServerDialect
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Database Platform
spring.jpa.database-platform=org.hibernate.dialect.SQLServerDialect
spring.jpa.open-in-view=false

# ===================================================================
# SECURITY CONFIGURATION
# ===================================================================

# JWT Configuration
jwt.secret=FinTrack-JWT-Secret-Key-Change-This-In-Production-2024
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# CORS Configuration
cors.allowed-origins=http://localhost:3000,http://localhost:8080,http://localhost:4200
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# ===================================================================
# LOGGING CONFIGURATION
# ===================================================================

# Root Logging Level
logging.level.root=INFO

# Application Logging
logging.level.za.co.fintrack=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=INFO

# SQL Logging (for development)
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# File Logging
logging.file.name=logs/fintrack.log
logging.file.max-size=10MB
logging.file.max-history=30

# ===================================================================
# ACTUATOR / MONITORING CONFIGURATION
# ===================================================================

# Management Endpoints
management.endpoints.web.exposure.include=health,info,metrics,prometheus,env
management.endpoint.health.show-details=when-authorized
management.endpoint.health.show-components=always
management.info.env.enabled=true

# Health Check Configuration
management.health.db.enabled=true
management.health.diskspace.enabled=true

# ===================================================================
# VALIDATION CONFIGURATION
# ===================================================================

# Bean Validation
spring.jpa.properties.javax.persistence.validation.mode=auto

# ===================================================================
# INTERNATIONALIZATION
# ===================================================================

# Locale Configuration
spring.web.locale=en_ZA
spring.web.locale-resolver=fixed

# ===================================================================
# PROFILE-SPECIFIC CONFIGURATIONS
# ===================================================================

# Active Profile (can be overridden by environment variable)
spring.profiles.active=dev
