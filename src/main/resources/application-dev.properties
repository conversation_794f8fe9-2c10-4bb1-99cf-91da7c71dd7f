# ===================================================================
# DEVELOPMENT PROFILE CONFIGURATION
# ===================================================================

# Development-specific database settings
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Enhanced logging for development
logging.level.za.co.fintrack=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Development CORS - Allow all origins for local development
cors.allowed-origins=*

# Development JWT settings (shorter expiration for testing)
jwt.expiration=3600000
jwt.refresh-expiration=86400000

# H2 Console (if needed for testing)
spring.h2.console.enabled=false

# Development-specific actuator settings
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

# Disable security for development endpoints (if needed)
management.security.enabled=false

# Development email settings (console output)
spring.mail.host=localhost
spring.mail.port=1025
spring.mail.username=
spring.mail.password=
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# File upload settings for development
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Development cache settings
spring.cache.type=simple

# Development async settings
spring.task.execution.pool.core-size=2
spring.task.execution.pool.max-size=4
spring.task.execution.pool.queue-capacity=100
