# ===================================================================
# PRODUCTION PROFILE CONFIGURATION
# ===================================================================
# NOTE: Override these values with environment variables in production

# Production Database Configuration
# Override with environment variables:
# DATABASE_URL, DATABASE_USERNAME, DATABASE_PASSWORD
spring.datasource.url=${DATABASE_URL:*************************************************************************************************}
spring.datasource.username=${DATABASE_USERNAME:sa}
spring.datasource.password=${DATABASE_PASSWORD:}

# Production Connection Pool Settings
spring.datasource.hikari.maximum-pool-size=${DB_POOL_MAX_SIZE:50}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:10}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFETIME:1800000}
spring.datasource.hikari.leak-detection-threshold=${DB_LEAK_DETECTION:60000}

# Production JPA Settings
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.hibernate.ddl-auto=validate

# Production JWT Configuration
jwt.secret=${JWT_SECRET:}
jwt.expiration=${JWT_EXPIRATION:86400000}
jwt.refresh-expiration=${JWT_REFRESH_EXPIRATION:604800000}

# Production CORS Configuration
cors.allowed-origins=${CORS_ALLOWED_ORIGINS:}
cors.allowed-methods=${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE}
cors.allowed-headers=${CORS_ALLOWED_HEADERS:Authorization,Content-Type}
cors.allow-credentials=${CORS_ALLOW_CREDENTIALS:false}

# Production Logging Configuration
logging.level.root=${LOG_LEVEL_ROOT:INFO}
logging.level.za.co.fintrack=${LOG_LEVEL_APP:INFO}
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN

# Production File Logging
logging.file.name=${LOG_FILE_PATH:/var/log/fintrack/fintrack.log}
logging.file.max-size=${LOG_FILE_MAX_SIZE:50MB}
logging.file.max-history=${LOG_FILE_MAX_HISTORY:30}

# Production Actuator Configuration
management.endpoints.web.exposure.include=${ACTUATOR_ENDPOINTS:health,info,metrics}
management.endpoint.health.show-details=${ACTUATOR_HEALTH_DETAILS:when-authorized}
management.security.enabled=true

# Production Server Configuration
server.port=${SERVER_PORT:8080}
server.servlet.context-path=${SERVER_CONTEXT_PATH:/api/v1}

# Production SSL Configuration (if using HTTPS)
server.ssl.enabled=${SSL_ENABLED:false}
server.ssl.key-store=${SSL_KEYSTORE:}
server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD:}
server.ssl.key-store-type=${SSL_KEYSTORE_TYPE:PKCS12}

# Production Email Configuration
spring.mail.host=${MAIL_HOST:}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=${MAIL_SMTP_AUTH:true}
spring.mail.properties.mail.smtp.starttls.enable=${MAIL_SMTP_STARTTLS:true}

# Production File Upload Settings
spring.servlet.multipart.max-file-size=${UPLOAD_MAX_FILE_SIZE:5MB}
spring.servlet.multipart.max-request-size=${UPLOAD_MAX_REQUEST_SIZE:5MB}

# Production Cache Configuration
spring.cache.type=${CACHE_TYPE:caffeine}

# Production Async Configuration
spring.task.execution.pool.core-size=${ASYNC_CORE_POOL_SIZE:10}
spring.task.execution.pool.max-size=${ASYNC_MAX_POOL_SIZE:20}
spring.task.execution.pool.queue-capacity=${ASYNC_QUEUE_CAPACITY:500}

# Production Security Headers
server.servlet.session.cookie.secure=${COOKIE_SECURE:true}
server.servlet.session.cookie.http-only=${COOKIE_HTTP_ONLY:true}
server.servlet.session.cookie.same-site=${COOKIE_SAME_SITE:strict}

# Production Rate Limiting
rate-limit.enabled=${RATE_LIMIT_ENABLED:true}
rate-limit.requests-per-minute=${RATE_LIMIT_RPM:100}

# Production Monitoring
management.metrics.export.prometheus.enabled=${PROMETHEUS_ENABLED:true}
management.tracing.sampling.probability=${TRACING_SAMPLING:0.1}
