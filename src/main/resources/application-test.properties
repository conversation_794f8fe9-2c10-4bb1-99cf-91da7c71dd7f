# ===================================================================
# TEST PROFILE CONFIGURATION
# ===================================================================

# H2 In-Memory Database for Testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console for test debugging
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration for Testing
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Disable connection pool for tests
spring.datasource.hikari.maximum-pool-size=1
spring.datasource.hikari.minimum-idle=1

# Test JWT Configuration
jwt.secret=test-jwt-secret-key-for-testing-only
jwt.expiration=3600000
jwt.refresh-expiration=7200000

# Logging for Tests
logging.level.root=WARN
logging.level.za.co.fintrack=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN

# Disable file logging for tests
logging.file.name=

# Test Actuator Configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=never

# Disable email for tests
spring.mail.host=
spring.mail.port=
spring.mail.username=
spring.mail.password=

# Test-specific settings
spring.test.database.replace=none

# Disable async processing for tests
spring.task.execution.pool.core-size=1
spring.task.execution.pool.max-size=1

# Cache configuration for tests
spring.cache.type=none

# Disable security for test endpoints
management.security.enabled=false

# Test CORS settings
cors.allowed-origins=http://localhost:8080
